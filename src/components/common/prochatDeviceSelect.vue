<template>
  <el-form-item class="form-item-ellipsis prochat-device-select">
    <template #label>
      <span
        class="form-item-label"
        :title="$t('dialog.prochatDeviceList')"
      >{{ $t('dialog.prochatDeviceList') }}</span>
    </template>
    <el-select
      v-model="prochatDeviceInfoRef.value"
      filterable
      :placeholder="$t('dialog.select')"
      :no-match-text="$t('dialog.noMatchText')"
      value-key="selfId"
      @change="onProchatDeviceInfoChanged"
    >
      <el-option
        v-for="(item, i) in prochatDeviceInfoList"
        :key="i"
        :label="item.selfId"
        :value="item"
      />
    </el-select>
    <el-button
      type="primary"
      icon="refresh-right"
      @click="queryProchatDeviceInfoList"
    />
  </el-form-item>
</template>

<script setup>
import { getProchatDeviceInfoList, gprochatDeviceInfoList, prochatDeviceInfo } from '@/utils/prochatDeviceInfoList'
import { debounce } from 'lodash'

const emit = defineEmits(['update-prochat-info'])

const prochatDeviceInfoList = gprochatDeviceInfoList
const prochatDeviceInfoRef = prochatDeviceInfo

const queryProchatDeviceInfoList = debounce(getProchatDeviceInfoList, 300)

function onProchatDeviceInfoChanged(val) {
  emit('update-prochat-info', val)
}

// 首次加载时自动拉取
if (!prochatDeviceInfoList || prochatDeviceInfoList.length === 0) {
  getProchatDeviceInfoList()
}
</script>

<style lang="scss">
  .prochat-device-select .el-form-item__content {
    display: flex;
    flex-wrap: nowrap;

    & > .el-select {
      flex: auto;

      .el-select__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    & > .el-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
</style>
